#!/usr/bin/env python3
"""
量化交易系统 - FastAPI 后端服务
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import sys
import os
import asyncio
import logging
from functools import lru_cache
import hashlib
import json

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

from src.data.data_manager import DataManager
from src.strategy.strategies import (
    MovingAverageCrossover, RSIStrategy, BollingerBands, BuyAndHold
)
from src.strategy.advanced_strategies import (
    MACDStrategy, MeanReversionStrategy, VWAPStrategy, MomentumStrategy
)
from src.backtest.backtester import Backtester
from src.analytics.dashboard import PerformanceAnalyzer
from src.utils.validators import (
    validate_symbol, validate_date_range, validate_strategy_parameters, validate_backtest_params
)
from src.utils.exceptions import ValidationError, DataError, StrategyError, BacktestError
from src.utils.config import setup_logging, get_config
from src.utils.json_utils import clean_data_for_json
from src.utils.performance import performance_monitor, timing_decorator
from src.utils.cache import cache_manager, cached
from src.utils.error_handler import error_handler, handle_errors
from src.monitoring.health_check import health_checker
from src.monitoring.metrics import metrics_collector, app_monitor, system_monitor, track_performance
from src.security.validation import security_validator

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

# 获取配置
config = get_config()

# 创建FastAPI应用
app = FastAPI(
    title="量化交易系统API",
    description="专业的量化交易策略回测系统",
    version="3.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局数据管理器
data_manager = DataManager()

# 缓存函数
@lru_cache(maxsize=100)
def get_cached_data(symbol: str, start_date_str: str, end_date_str: str):
    """缓存数据获取"""
    start_date = datetime.fromisoformat(start_date_str) if start_date_str else None
    end_date = datetime.fromisoformat(end_date_str) if end_date_str else None
    return data_manager.get_historical_data(symbol, start_date, end_date)

def generate_cache_key(symbol: str, strategy: str, parameters: dict,
                      start_date: str, end_date: str,
                      initial_capital: float, commission: float, slippage: float) -> str:
    """生成缓存键"""
    cache_data = {
        'symbol': symbol,
        'strategy': strategy,
        'parameters': parameters,
        'start_date': start_date,
        'end_date': end_date,
        'initial_capital': initial_capital,
        'commission': commission,
        'slippage': slippage
    }
    cache_str = json.dumps(cache_data, sort_keys=True)
    return hashlib.md5(cache_str.encode()).hexdigest()

# 策略映射
STRATEGIES = {
    "moving_average": MovingAverageCrossover,
    "rsi": RSIStrategy,
    "bollinger_bands": BollingerBands,
    "buy_and_hold": BuyAndHold,
    "macd": MACDStrategy,
    "mean_reversion": MeanReversionStrategy,
    "vwap": VWAPStrategy,
    "momentum": MomentumStrategy
}

# Pydantic模型
class BacktestRequest(BaseModel):
    symbol: str
    strategy: str
    parameters: Dict[str, Any] = {}
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    initial_capital: float = 10000
    commission: float = 0.001
    slippage: float = 0.001

class BacktestResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class StrategyInfo(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]

class MarketDataRequest(BaseModel):
    symbol: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    interval: str = "1d"

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "量化交易系统API", "version": "3.0.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/strategies", response_model=List[StrategyInfo])
async def get_strategies():
    """获取所有可用策略"""
    strategies = [
        {
            "name": "moving_average",
            "description": "移动均线交叉策略",
            "parameters": {
                "fast_period": {"type": "int", "default": 20, "min": 5, "max": 50},
                "slow_period": {"type": "int", "default": 50, "min": 20, "max": 200}
            }
        },
        {
            "name": "rsi",
            "description": "RSI相对强弱指标策略",
            "parameters": {
                "period": {"type": "int", "default": 14, "min": 5, "max": 30},
                "oversold": {"type": "int", "default": 30, "min": 10, "max": 40},
                "overbought": {"type": "int", "default": 70, "min": 60, "max": 90}
            }
        },
        {
            "name": "bollinger_bands",
            "description": "布林带策略",
            "parameters": {
                "period": {"type": "int", "default": 20, "min": 10, "max": 50},
                "num_std": {"type": "float", "default": 2.0, "min": 1.0, "max": 3.0}
            }
        },
        {
            "name": "buy_and_hold",
            "description": "买入持有策略",
            "parameters": {}
        },
        {
            "name": "macd",
            "description": "MACD策略",
            "parameters": {
                "fast_period": {"type": "int", "default": 12, "min": 5, "max": 20},
                "slow_period": {"type": "int", "default": 26, "min": 20, "max": 50},
                "signal_period": {"type": "int", "default": 9, "min": 5, "max": 15}
            }
        },
        {
            "name": "mean_reversion",
            "description": "均值回归策略",
            "parameters": {
                "lookback_period": {"type": "int", "default": 20, "min": 10, "max": 50},
                "entry_threshold": {"type": "float", "default": 2.0, "min": 1.0, "max": 3.0}
            }
        },
        {
            "name": "vwap",
            "description": "VWAP策略",
            "parameters": {
                "period": {"type": "int", "default": 20, "min": 10, "max": 50}
            }
        },
        {
            "name": "momentum",
            "description": "动量策略",
            "parameters": {
                "fast_window": {"type": "int", "default": 10, "min": 5, "max": 30},
                "slow_window": {"type": "int", "default": 30, "min": 20, "max": 100}
            }
        }
    ]
    return strategies

@app.post("/market-data")
@timing_decorator
async def get_market_data(request: MarketDataRequest):
    """获取市场数据"""
    try:
        # 解析日期
        start_date = None
        end_date = None
        
        if request.start_date:
            start_date = datetime.fromisoformat(request.start_date.replace('Z', '+00:00'))
        if request.end_date:
            end_date = datetime.fromisoformat(request.end_date.replace('Z', '+00:00'))
        
        # 获取数据
        data = data_manager.get_historical_data(
            symbol=request.symbol,
            start_date=start_date,
            end_date=end_date,
            interval=request.interval
        )
        
        if data.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {request.symbol}")
        
        # 处理NaN值并转换为JSON格式
        data_dict = {
            "symbol": request.symbol,
            "data": clean_data_for_json(data.reset_index()),
            "count": len(data)
        }
        
        return {"success": True, "data": data_dict}
        
    except Exception as e:
        logger.error(f"Error fetching market data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/backtest", response_model=BacktestResponse)
@timing_decorator
def run_backtest(request: BacktestRequest):
    """运行回测"""
    logger.info(f"Starting backtest for {request.symbol} with strategy {request.strategy}")

    try:
        # 验证策略
        if request.strategy not in STRATEGIES:
            logger.warning(f"Unknown strategy requested: {request.strategy}")
            raise HTTPException(status_code=400, detail=f"Unknown strategy: {request.strategy}")

        # 验证参数
        if request.initial_capital <= 0:
            raise HTTPException(status_code=400, detail="Initial capital must be positive")

        # 解析日期
        start_date = None
        end_date = None

        if request.start_date:
            start_date = datetime.fromisoformat(request.start_date.replace('Z', '+00:00'))
        if request.end_date:
            end_date = datetime.fromisoformat(request.end_date.replace('Z', '+00:00'))

        # 验证日期范围
        if start_date and end_date and start_date >= end_date:
            raise HTTPException(status_code=400, detail="Start date must be before end date")

        logger.info(f"Fetching data for {request.symbol} from {start_date} to {end_date}")

        # 获取数据
        data = data_manager.get_historical_data(
            symbol=request.symbol,
            start_date=start_date,
            end_date=end_date
        )

        if data.empty:
            logger.warning(f"No data found for symbol {request.symbol}")
            raise HTTPException(status_code=404, detail=f"No data found for symbol {request.symbol}")

        logger.info(f"Retrieved {len(data)} data points")

        # 创建策略实例
        strategy_class = STRATEGIES[request.strategy]

        try:
            if request.strategy == "moving_average":
                fast_period = request.parameters.get('fast_period', 20)
                slow_period = request.parameters.get('slow_period', 50)
                if fast_period >= slow_period:
                    raise ValueError("Fast period must be less than slow period")
                strategy = strategy_class(fast_period=fast_period, slow_period=slow_period)
            elif request.strategy == "rsi":
                period = request.parameters.get('period', 14)
                oversold = request.parameters.get('oversold', 30)
                overbought = request.parameters.get('overbought', 70)
                if oversold >= overbought:
                    raise ValueError("Oversold threshold must be less than overbought threshold")
                strategy = strategy_class(period=period, oversold=oversold, overbought=overbought)
            elif request.strategy == "bollinger_bands":
                strategy = strategy_class(
                    period=request.parameters.get('period', 20),
                    num_std=request.parameters.get('num_std', 2.0)
                )
            elif request.strategy == "macd":
                fast_period = request.parameters.get('fast_period', 12)
                slow_period = request.parameters.get('slow_period', 26)
                if fast_period >= slow_period:
                    raise ValueError("MACD fast period must be less than slow period")
                strategy = strategy_class(
                    fast=fast_period,
                    slow=slow_period,
                    signal=request.parameters.get('signal_period', 9)
                )
            elif request.strategy == "mean_reversion":
                strategy = strategy_class(
                    lookback=request.parameters.get('lookback_period', 20),
                    z_threshold=request.parameters.get('entry_threshold', 2.0)
                )
            elif request.strategy == "vwap":
                strategy = strategy_class(
                    window=request.parameters.get('period', 20)
                )
            elif request.strategy == "momentum":
                fast_window = request.parameters.get('fast_window', 10)
                slow_window = request.parameters.get('slow_window', 30)
                if fast_window >= slow_window:
                    raise ValueError("Momentum fast window must be less than slow window")
                strategy = strategy_class(
                    fast_window=fast_window,
                    slow_window=slow_window
                )
            else:
                strategy = strategy_class()
        except ValueError as ve:
            logger.warning(f"Invalid strategy parameters: {ve}")
            raise HTTPException(status_code=400, detail=str(ve))

        logger.info(f"Running backtest with strategy: {strategy.name}")

        # 运行回测
        backtester = Backtester(
            initial_capital=request.initial_capital,
            commission=request.commission,
            slippage=request.slippage
        )

        results = backtester.run(strategy, data)

        # 处理结果
        if 'portfolio' in results:
            # 处理NaN值并转换portfolio为JSON格式
            portfolio = results['portfolio'].fillna(0).reset_index()
            results['portfolio'] = portfolio.to_dict('records')

        # 计算额外的分析指标
        analyzer = PerformanceAnalyzer(results)
        metrics = analyzer.calculate_metrics()
        results.update(metrics)

        logger.info(f"Backtest completed successfully. Total return: {results.get('total_return', 0):.2%}")

        return BacktestResponse(success=True, data=results)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error running backtest: {e}", exc_info=True)
        return BacktestResponse(success=False, error=f"Internal server error: {str(e)}")

@app.get("/symbols/search")
async def search_symbols(query: str):
    """搜索股票代码"""
    # 常见股票代码列表
    common_symbols = [
        {"symbol": "AAPL", "name": "Apple Inc.", "exchange": "NASDAQ"},
        {"symbol": "MSFT", "name": "Microsoft Corporation", "exchange": "NASDAQ"},
        {"symbol": "GOOGL", "name": "Alphabet Inc.", "exchange": "NASDAQ"},
        {"symbol": "AMZN", "name": "Amazon.com Inc.", "exchange": "NASDAQ"},
        {"symbol": "TSLA", "name": "Tesla Inc.", "exchange": "NASDAQ"},
        {"symbol": "META", "name": "Meta Platforms Inc.", "exchange": "NASDAQ"},
        {"symbol": "NVDA", "name": "NVIDIA Corporation", "exchange": "NASDAQ"},
        {"symbol": "JPM", "name": "JPMorgan Chase & Co.", "exchange": "NYSE"},
        {"symbol": "JNJ", "name": "Johnson & Johnson", "exchange": "NYSE"},
        {"symbol": "V", "name": "Visa Inc.", "exchange": "NYSE"},
        {"symbol": "PG", "name": "Procter & Gamble Co.", "exchange": "NYSE"},
        {"symbol": "UNH", "name": "UnitedHealth Group Inc.", "exchange": "NYSE"},
        {"symbol": "HD", "name": "Home Depot Inc.", "exchange": "NYSE"},
        {"symbol": "MA", "name": "Mastercard Inc.", "exchange": "NYSE"},
        {"symbol": "BAC", "name": "Bank of America Corp.", "exchange": "NYSE"},
    ]

    # 简单的搜索过滤
    query = query.upper()
    filtered = [s for s in common_symbols
                if query in s["symbol"] or query in s["name"].upper()]

    return {"symbols": filtered[:10]}  # 限制返回10个结果

@app.get("/performance/compare")
async def compare_strategies(
    symbol: str,
    strategies: str,  # 逗号分隔的策略列表
    start_date: str = None,
    end_date: str = None
):
    """比较多个策略的性能"""
    try:
        strategy_list = strategies.split(',')
        results = {}

        # 获取数据
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00')) if start_date else None
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00')) if end_date else None

        data = data_manager.get_historical_data(symbol, start_dt, end_dt)
        if data.empty:
            raise HTTPException(status_code=404, detail=f"No data found for symbol {symbol}")

        # 为每个策略运行回测
        for strategy_name in strategy_list:
            if strategy_name.strip() in STRATEGIES:
                strategy_class = STRATEGIES[strategy_name.strip()]
                strategy = strategy_class()

                backtester = Backtester(initial_capital=10000)
                result = backtester.run(strategy, data)

                # 只保留关键指标
                results[strategy_name.strip()] = {
                    'total_return': result.get('total_return', 0),
                    'annualized_return': result.get('annualized_return', 0),
                    'sharpe_ratio': result.get('sharpe_ratio', 0),
                    'max_drawdown': result.get('max_drawdown', 0),
                    'num_trades': result.get('num_trades', 0)
                }

        return {"success": True, "data": results}

    except Exception as e:
        logger.error(f"Error comparing strategies: {e}")
        return {"success": False, "error": str(e)}

@app.get("/system/status")
async def system_status():
    """轻量级系统状态检查"""
    try:
        import psutil
        
        # 获取系统基本信息
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "api": "healthy",
                "strategies": len(STRATEGIES),
                "data_manager": "healthy",
                "cache": "healthy"
            },
            "system_info": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": round(memory.available / (1024**3), 2)
            },
            "version": "3.0.0"
        }
    except Exception as e:
        logger.error(f"System status check failed: {e}", exc_info=True)
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "version": "3.0.0"
        }

@app.get("/system/detailed-status")
async def detailed_system_status():
    """详细的系统状态检查（可能较慢）"""
    try:
        # 使用健康检查器进行全面检查
        health_results = health_checker.run_all_checks("http://localhost:8000")
        
        return {
            "status": health_results["overall_status"],
            "timestamp": datetime.now().isoformat(),
            "health_checks": health_results["checks"],
            "system_info": health_results["system_info"],
            "components": {
                "api": "healthy",
                "strategies": len(STRATEGIES)
            },
            "version": "3.0.0"
        }
    except Exception as e:
        logger.error(f"Detailed system status check failed: {e}", exc_info=True)
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "version": "3.0.0"
        }

@app.get("/monitoring/performance")
async def get_performance_metrics():
    """获取性能指标"""
    try:
        return {
            "success": True,
            "data": {
                "system_info": performance_monitor.get_system_info(),
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Performance metrics error: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/monitoring/health")
async def comprehensive_health_check():
    """综合健康检查"""
    try:
        return {
            "success": True,
            "data": health_checker.run_all_checks("http://localhost:8000")
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
